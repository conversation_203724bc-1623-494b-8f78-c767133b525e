#include "key_driver.h"

int key_read(void)
{
	uint8_t temp = 0;
	
	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_RESET) temp = 1;
	if(HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin) == GPIO_PIN_RESET) temp = 2;
	if(HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET) temp = 3;
	if(HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET) temp = 4;
	if(HAL_GPIO_ReadPin(USER_GPIO_Port, USER_Pin) == GPIO_PIN_RESET) temp = 10;//USER
	
	return temp;
}
