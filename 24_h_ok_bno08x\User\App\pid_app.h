#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "mydefine.h"
#include "pid.h"

// PID�����ṹ��
typedef struct
{
    float kp;          // ����ϵ��
    float ki;          // ����ϵ��
    float kd;          // ΢��ϵ��
    float out_min;     // �����Сֵ
    float out_max;     // ������ֵ
} PidParams_t;

void PID_Init(void);
void PID_Task(void);
void PID_Enable(bool enable);     // PID使能控制函数
bool PID_IsRunning(void);         // 获取PID运行状态

extern bool pid_running; // PID ����ʹ�ܿ���
extern unsigned char pid_control_mode;

extern int basic_speed;

extern PID_T pid_speed_left;  // �����ٶȻ�
extern PID_T pid_speed_right; // �����ٶȻ�

extern PID_T pid_line;        // ѭ����

extern PID_T pid_angle;       // �ǶȻ�

#endif
